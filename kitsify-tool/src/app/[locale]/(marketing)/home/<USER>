'use client';

import type { getTranslations } from 'next-intl/server';

import { useTranslations } from 'next-intl';

import dynamic from 'next/dynamic';

import Image from 'next/image';
import { useParams } from 'next/navigation';
import React, { useEffect, useState } from 'react';
import { GoCreditCard } from 'react-icons/go';
import { IoMailSharp } from 'react-icons/io5';
import { Autoplay } from 'swiper/modules';
import { Swiper, SwiperSlide } from 'swiper/react';
import 'swiper/css';
import 'swiper/css/pagination';
import 'swiper/css/navigation';

const Header = dynamic(() => import('@/components/Header'));
const BenefitsSection = dynamic(() => import('@/components/BenefitsSection'));
const ComparisionTools = dynamic(() => import('@/components/ComparisonTools'));
const PricingTable = dynamic(() => import('@/components/PricingTable'));
const StepGuide = dynamic(() => import('@/components/StepGuide'));
const Feedback = dynamic(() => import('@/components/Feedback'));
const PricingPlan = dynamic(() => import('@/components/PricingPlan'));
const FAQ = dynamic(() => import('@/components/FAQ'));
const MainFooter = dynamic(() => import('@/components/Footer'));
const PaymentModal = dynamic(() => import('@/components/PaymentModal'));
const GiftBanner = dynamic(() => import('@/components/GiftBanner'));

type HomeProps = {
  t?: ReturnType<typeof getTranslations>;
};

const images = [
  '/assets/images/tools1.webp',
  '/assets/images/tools2.webp',
  '/assets/images/tools3.webp',
  '/assets/images/tools4.webp',
  '/assets/images/tools1.webp',
];

export const Home: React.FC<HomeProps> = () => {
  const t = useTranslations('Index');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [showGiftBanner, setShowGiftBanner] = useState(false);
  const params = useParams();
  const locale = params?.locale as string;

  React.useEffect(() => {
    // Only access localStorage on client side
    if (typeof window !== 'undefined') {
      localStorage.setItem('locale', locale);
    }
  }, [locale]);

  // Gift banner timer effect
  useEffect(() => {
    const timer = setTimeout(() => {
      setShowGiftBanner(true);
    }, 30000); // 30 seconds

    return () => clearTimeout(timer);
  }, []);

  return (
    <>
      <Header />
      <section id="top" className="bg-gray-100 pb-16 pt-32 text-center">
        <div className="container mx-auto">
          <h1 className="mb-6 text-4xl font-bold">{t('hero_title')}</h1>
          <p className="mb-4 text-xl">{t('hero_subtitle')}</p>
          <div className="w-full">
            <Swiper
              modules={[Autoplay]}
              loop
              autoplay={{
                delay: 500,
                disableOnInteraction: false,
              }}
              speed={4000}
              breakpoints={{
                480: {
                  slidesPerView: 1,
                  spaceBetween: 10,
                },
                768: {
                  slidesPerView: 4,
                  spaceBetween: 20,
                },
                1024: {
                  slidesPerView: 4,
                  spaceBetween: 20,
                },
              }}
            >
              {images.map((src, index) => (
                <SwiperSlide key={`index ${index.toString()}`}>
                  <div className="relative h-[220px]">
                    <Image
                      src={src}
                      className="rounded-md object-contain"
                      alt={`Slide ${index + 1}`}
                      fill
                      priority={index < 4}
                      sizes="w-[240px] h-[250px]"
                    />
                  </div>
                </SwiperSlide>
              ))}
            </Swiper>
          </div>
        </div>
      </section>
      <section className="py-16 text-center">
        <div className="container mx-auto">
          <h1 className="mb-6 text-center text-4xl font-bold">
            {t('features_title')}
          </h1>
          <p className="mb-4 px-9 text-xl">
            {t('features_subtitle')}
          </p>
          <div className="grid grid-cols-1 gap-3 md:mt-9 md:grid-cols-[30%,40%,30%]">
            <div className="flex-col">
              <div className="mb-4 flex flex-col place-items-center md:mt-8 md:h-36">
                <IoMailSharp size={40} />
                <h2 className="text-lg font-bold uppercase">
                  {t('OPTIMIZE_WORK')}
                </h2>
                <p>{t('MORE_THAN_25_TOOLS')}</p>
              </div>
              <div className="flex flex-col place-items-center md:h-36">
                <GoCreditCard size={40} />
                <h2 className="text-lg font-bold uppercase">
                  {t('SAVE_A_LOT')}
                </h2>
                <p>{t('TOOLS_WORTH_3000_USD')}</p>
              </div>
            </div>

            <div className="relative h-[300px] md:h-auto">
              <Image
                src="/assets/images/tools5.webp"
                alt="tools"
                fill
                sizes="(max-width: 768px) 100vw, 50vw"
              />
            </div>

            <div className="flex-col">
              <div className="mb-4 flex flex-col place-items-center md:mt-8 md:h-36">
                <IoMailSharp size={40} />
                <h2 className="text-lg font-bold uppercase">
                  {t('DETECT_OPPORTUNITIES')}
                </h2>
                <p>{t('ANALYZE_TRENDS')}</p>
              </div>
              <div className="flex flex-col place-items-center md:h-36">
                <IoMailSharp size={40} />
                <h2 className="text-lg font-bold uppercase">
                  {t('FAST_LOGIN')}
                </h2>
                <p>{t('NO_MORE_COPY_PASTE')}</p>
              </div>
            </div>
          </div>
        </div>
      </section>
      <section className="bg-gray-50 pt-16 text-center">
        <div className="container mx-auto">
          <h1 className="mb-6 text-4xl font-bold">
            {t('NOT_JUST_A_TOOL')}
          </h1>
          <p className="mb-8 text-xl">
            {t('UPGRADE_NOW')}
          </p>
          <button
            type="button"
            className="rounded-lg bg-gradient-to-r from-cyan-500 to-blue-500 px-5 py-2.5 text-center text-lg font-medium text-white hover:bg-gradient-to-bl focus:outline-none focus:ring-4 focus:ring-cyan-300 dark:focus:ring-cyan-800"
            onClick={() => setIsModalOpen(true)}
          >
            {t('UPGRADE_VIP_MEMBER')}
          </button>
        </div>
      </section>
      <section id="tools" className="bg-gray-50 py-16 text-center">
        <div className="container mx-auto lg:px-64">
          <h1 className="mb-6 text-4xl font-bold">
            {t('WHAT_DOES_KITSIFY_TOOLS_HAVE')}
          </h1>
          <PricingTable />
        </div>
      </section>
      <section className="py-12 sm:text-center md:text-start">
        <div className="container mx-auto md:px-4">
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
            <div className="relative h-[250px] md:h-[400px]">
              <Image
                src="/assets/images/kitsify_intro.gif"
                alt="Kitsify Guide"
                fill
                sizes="(max-width: 768px) 100vw, 50vw"
              />
            </div>
            <div className="mx-5 flex flex-col">
              <h1 className="mb-4 text-4xl font-bold">
                {t('SAVE_TIME_MONEY_EFFORT')}
              </h1>
              <p className="mb-4 text-xl">
                {t('PREVIOUSLY_TOOLS')}
                <br />
                <br />
                {t('NOW_WITH_ONE_CLICK')}
              </p>
              <button
                type="button"
                className="mb-2 me-2 rounded-lg border border-green-700 px-5 py-2.5 text-center text-lg font-medium text-green-700 hover:bg-green-800 hover:text-white focus:outline-none focus:ring-4 focus:ring-green-300 dark:border-green-500 dark:text-green-500 dark:hover:bg-green-600 dark:hover:text-white dark:focus:ring-green-800"
                onClick={() => setIsModalOpen(true)}
              >
                {t('UPGRADE_NOW_BUTTON')}
              </button>
            </div>
          </div>
        </div>
      </section>
      <section className="bg-gray-100 pt-16 text-center">
        <div className="container mx-auto">
          <h1 className="mb-6 text-4xl font-bold">
            {t('ONE_PACKAGE_FOR_ALL')}
          </h1>
          <p className="mb-4 text-xl">
            {t('MORE_THAN_33_TOOLS')}
          </p>
          <div className="grid grid-cols-1 gap-6 md:grid-cols-3 xl:grid-cols-4">
            <div className="flex flex-col">
              <div className="relative mb-10 min-h-56">
                <Image
                  className="rounded-lg"
                  src="/assets/images/tools1.webp"
                  alt="tools"
                  fill
                  sizes="(max-width: 768px) 100vw, 50vw"
                />
                <span
                  className="absolute bottom-0 left-1/2 flex size-16 -translate-x-1/2 -translate-y-1/2 items-center justify-center rounded-lg border bg-white text-2xl font-bold"
                  style={{ transform: 'translate(-50%, 50%)' }}
                >
                  1
                </span>
              </div>
              <h2 className="mb-2 text-xl font-bold uppercase">
                {t('DROPSHIPPERS')}
              </h2>
              <p className="px-2 text-lg">
                {t('FIND_WINNING_PRODUCTS')}
              </p>
            </div>
            <div className="flex flex-col">
              <div className="relative mb-10 min-h-56">
                <Image
                  className="rounded-lg"
                  src="/assets/images/tools2.webp"
                  alt="tools"
                  fill
                  sizes="(max-width: 768px) 100vw, 50vw"
                />
                <span
                  className="absolute bottom-0 left-1/2 flex size-16 -translate-x-1/2 -translate-y-1/2 items-center justify-center rounded-lg border bg-white text-2xl font-bold"
                  style={{ transform: 'translate(-50%, 50%)' }}
                >
                  2
                </span>
              </div>
              <h2 className="mb-2 text-xl font-bold uppercase">
                {t('CONTENT_CREATORS')}
              </h2>
              <p className="px-2 text-lg">
                {t('PRO_EDITING_TOOLS')}
              </p>
            </div>
            <div className="flex flex-col">
              <div className="relative mb-10 min-h-56">
                <Image
                  className="rounded-lg"
                  src="/assets/images/tools3.webp"
                  alt="tools"
                  fill
                  sizes="(max-width: 768px) 100vw, 50vw"
                />
                <span
                  className="absolute bottom-0 left-1/2 flex size-16 -translate-x-1/2 -translate-y-1/2 items-center justify-center rounded-lg border bg-white text-center text-2xl font-bold"
                  style={{ transform: 'translate(-50%, 50%)' }}
                >
                  3
                </span>
              </div>
              <h2 className="mb-2 text-xl font-bold uppercase">
                {t('SEO')}
              </h2>
              <p className="px-2 text-lg">
                {t('SEO_TOOLS')}
              </p>
            </div>
            <div className="flex flex-col">
              <div className="relative mb-10 min-h-56">
                <Image
                  className="rounded-lg"
                  src="/assets/images/tools4.webp"
                  alt="tools"
                  fill
                  sizes="(max-width: 768px) 100vw, 50vw"
                />
                <span
                  className="absolute bottom-0 left-1/2 flex size-16 -translate-x-1/2 -translate-y-1/2 items-center justify-center rounded-lg border bg-white text-center text-2xl font-bold"
                  style={{ transform: 'translate(-50%, 50%)' }}
                >
                  4
                </span>
              </div>
              <h2 className="mb-2 text-xl font-bold uppercase">
                {t('FREELANCERS')}
              </h2>
              <p className="px-2 text-lg">
                {t('ADVANCED_TOOLS')}
              </p>
            </div>
          </div>
        </div>
      </section>
      <section className="bg-gray-100 py-16 text-center">
        <div className="container mx-auto">
          <div className="m-auto px-5 md:w-1/2">
            <h1 className="mb-6 text-4xl font-bold">
              {t('UPGRADE_NOW_PROMO')}
            </h1>
            <p className="mb-10 text-xl">
              {t('KITSIFY_TOOLS_COMPANION')}
            </p>
            <button
              type="button"
              className="rounded-lg bg-gradient-to-r from-cyan-500 to-blue-500 px-5 py-2.5 text-center text-lg font-medium text-white hover:bg-gradient-to-bl focus:outline-none focus:ring-4 focus:ring-cyan-300 dark:focus:ring-cyan-800"
              onClick={() => setIsModalOpen(true)}
            >
              {t('UPGRADE_VIP_MEMBER')}
            </button>
          </div>
        </div>
      </section>
      <section className="py-16 text-center shadow-sm">
        <div className="container mx-auto">
          <h1 className="mb-6 text-4xl font-bold">
            {t('NOT_JUST_A_TOOL_ALL_IN_ONE')}
          </h1>
          <p className="mb-4 text-xl">
            {t('ACCESS_ALL_RESOURCES')}
          </p>
          <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
            <div className="flex flex-col">
              <div className="relative mb-10 min-h-56">
                <Image
                  className="rounded-lg"
                  src="/assets/images/gift1.webp"
                  alt="gifts"
                  fill
                  sizes="(max-width: 768px) 100vw, 50vw"
                />
                <span
                  className="absolute bottom-0 left-1/2 flex size-16 -translate-x-1/2 -translate-y-1/2 items-center justify-center rounded-lg border bg-white text-2xl font-bold"
                  style={{ transform: 'translate(-50%, 50%)' }}
                >
                  1
                </span>
              </div>
              <h2 className="mb-2 text-xl font-bold uppercase">
                {t('HIGH_CONVERSION_ADS_TEMPLATES')}
              </h2>
              <p className="px-2 text-lg">
                {t('INCLUDES_ADS_TEMPLATES')}
              </p>
            </div>
            <div className="flex flex-col">
              <div className="relative mb-10 min-h-56">
                <Image
                  className="rounded-lg"
                  src="/assets/images/gift2.webp"
                  alt="gifts"
                  fill
                  sizes="(max-width: 768px) 100vw, 50vw"
                />
                <span
                  className="absolute bottom-0 left-1/2 flex size-16 -translate-x-1/2 -translate-y-1/2 items-center justify-center rounded-lg border bg-white text-2xl font-bold"
                  style={{ transform: 'translate(-50%, 50%)' }}
                >
                  2
                </span>
              </div>
              <h2 className="mb-2 text-xl font-bold uppercase">
                {t('DROPSHIPPING_THEMES')}
              </h2>
              <p className="px-2 text-lg">
                {t('HIGH_CONVERSION_THEMES')}
              </p>
            </div>
            <div className="flex flex-col">
              <div className="relative mb-10 min-h-56">
                <Image
                  className="rounded-lg"
                  src="/assets/images/gift3.webp"
                  alt="gifts"
                  fill
                  sizes="(max-width: 768px) 100vw, 50vw"
                />
                <span
                  className="absolute bottom-0 left-1/2 flex size-16 -translate-x-1/2 -translate-y-1/2 items-center justify-center rounded-lg border bg-white text-center text-2xl font-bold"
                  style={{ transform: 'translate(-50%, 50%)' }}
                >
                  3
                </span>
              </div>
              <h2 className="mb-2 text-xl font-bold uppercase">
                {t('ECOM_EBOOKS')}
              </h2>
              <p className="px-2 text-lg">
                {t('INCLUDES_ECOM_EBOOKS')}
              </p>
            </div>
            <div className="flex flex-col">
              <div className="relative mb-10 min-h-56">
                <Image
                  className="rounded-lg"
                  src="/assets/images/gift4.webp"
                  alt="gifts"
                  fill
                  sizes="(max-width: 768px) 100vw, 50vw"
                />
                <span
                  className="absolute bottom-0 left-1/2 flex size-16 -translate-x-1/2 -translate-y-1/2 items-center justify-center rounded-lg border bg-white text-center text-2xl font-bold"
                  style={{ transform: 'translate(-50%, 50%)' }}
                >
                  4
                </span>
              </div>
              <h2 className="mb-2 text-xl font-bold uppercase">
                {t('EDITABLE_TSHIRT_DESIGNS')}
              </h2>
              <p className="px-2 text-lg">
                {t('EDITABLE_TSHIRT_DESIGNS')}
              </p>
            </div>
            <div className="flex flex-col">
              <div className="relative mb-10 min-h-56">
                <Image
                  className="rounded-lg"
                  src="/assets/images/gift5.webp"
                  alt="gifts"
                  fill
                  sizes="(max-width: 768px) 100vw, 50vw"
                />
                <span
                  className="absolute bottom-0 left-1/2 flex size-16 -translate-x-1/2 -translate-y-1/2 items-center justify-center rounded-lg border bg-white text-center text-2xl font-bold"
                  style={{ transform: 'translate(-50%, 50%)' }}
                >
                  5
                </span>
              </div>
              <h2 className="mb-2 text-xl font-bold uppercase">
                ETSY LISTING TEMPLATES
              </h2>
              <p className="px-2 text-lg">
                {t('ETSY_LISTING_TEMPLATES')}
              </p>
            </div>
            <div className="flex flex-col">
              <div className="relative mb-10 min-h-56">
                <Image
                  className="rounded-lg object-cover"
                  src="/assets/images/gift6.png"
                  alt="gifts"
                  fill
                  sizes="(max-width: 768px) 100vw, 50vw"
                />
                <span
                  className="absolute bottom-0 left-1/2 flex size-16 -translate-x-1/2 -translate-y-1/2 items-center justify-center rounded-lg border bg-white text-center text-2xl font-bold"
                  style={{ transform: 'translate(-50%, 50%)' }}
                >
                  6
                </span>
              </div>
              <h2 className="mb-2 text-xl font-bold uppercase">
                {t('PROMPT_GENERATOR')}
              </h2>
              <p className="px-2 text-lg">
                {t('PROMPT_GENERATOR_DESC')}
              </p>
            </div>
          </div>
        </div>
      </section>
      <section className="py-16 shadow-sm">
        <div className="container mx-auto">
          <ComparisionTools />
        </div>
      </section>
      <section id="guide" className="py-16">
        <div className="container mx-auto lg:px-40">
          <h1 className="mb-6 text-center text-4xl font-bold uppercase">
            {t('USAGE_GUIDE')}
          </h1>
          <StepGuide />
        </div>
      </section>
      <section className="bg-gray-100 py-16">
        <div className="container mx-auto">
          <h1 className="mb-10 text-center text-4xl font-bold">
            {t('BENEFITS_YOU_CANT_IGNORE')}
          </h1>
          <BenefitsSection />
        </div>
      </section>
      <section id="pricing" className="pt-16">
        <div className="container mx-auto">
          <PricingPlan />
        </div>
      </section>
      <section className="py-16">
        <h1 className="mb-10 text-center text-4xl font-bold">
          {t('SOME_HIGHLIGHTED_REVIEWS')}
        </h1>
        <Feedback />
      </section>
      <FAQ />
      <MainFooter />
      <PaymentModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
      />
      <GiftBanner
        isVisible={showGiftBanner}
        onClose={() => setShowGiftBanner(false)}
      />
    </>
  );
};
